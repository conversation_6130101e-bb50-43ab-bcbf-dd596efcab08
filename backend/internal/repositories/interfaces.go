package repositories

import (
	"context"
	"database/sql"

	"cloudweave/internal/models"
)

// UserRepositoryInterface defines the contract for user data operations
type UserRepositoryInterface interface {
	Create(ctx context.Context, user *models.User) error
	GetByID(ctx context.Context, id string) (*models.User, error)
	GetByEmail(ctx context.Context, email string) (*models.User, error)
	Update(ctx context.Context, user *models.User) error
	UpdateLastLogin(ctx context.Context, userID string) error
	UpdatePassword(ctx context.Context, userID, passwordHash string) error
	Delete(ctx context.Context, id string) error
	EmailExists(ctx context.Context, email string) (bool, error)
	List(ctx context.Context, params ListParams) ([]*models.User, error)
}

// OrganizationRepositoryInterface defines the contract for organization data operations
type OrganizationRepositoryInterface interface {
	Create(ctx context.Context, org *models.Organization) error
	GetByID(ctx context.Context, id string) (*models.Organization, error)
	GetBySlug(ctx context.Context, slug string) (*models.Organization, error)
	Update(ctx context.Context, org *models.Organization) error
	Delete(ctx context.Context, id string) error
	List(ctx context.Context, params ListParams) ([]*models.Organization, error)
	SlugExists(ctx context.Context, slug string) (bool, error)
}

// InfrastructureRepositoryInterface defines the contract for infrastructure data operations
type InfrastructureRepositoryInterface interface {
	Create(ctx context.Context, infra *models.Infrastructure) error
	GetByID(ctx context.Context, id string) (*models.Infrastructure, error)
	Update(ctx context.Context, infra *models.Infrastructure) error
	Delete(ctx context.Context, id string) error
	List(ctx context.Context, orgID string, params ListParams) ([]*models.Infrastructure, error)
	ListByProvider(ctx context.Context, orgID, provider string, params ListParams) ([]*models.Infrastructure, error)
	ListByStatus(ctx context.Context, orgID, status string, params ListParams) ([]*models.Infrastructure, error)
	UpdateStatus(ctx context.Context, id, status string) error
	GetByExternalID(ctx context.Context, externalID string) (*models.Infrastructure, error)
}

// DeploymentRepositoryInterface defines the contract for deployment data operations
type DeploymentRepositoryInterface interface {
	Create(ctx context.Context, deployment *models.Deployment) error
	GetByID(ctx context.Context, id string) (*models.Deployment, error)
	Update(ctx context.Context, deployment *models.Deployment) error
	Delete(ctx context.Context, id string) error
	List(ctx context.Context, orgID string, params ListParams) ([]*models.Deployment, error)
	ListByEnvironment(ctx context.Context, orgID, environment string, params ListParams) ([]*models.Deployment, error)
	ListByStatus(ctx context.Context, orgID, status string, params ListParams) ([]*models.Deployment, error)
	UpdateStatus(ctx context.Context, id, status string) error
	UpdateProgress(ctx context.Context, id string, progress int) error
}

// MetricRepositoryInterface defines the contract for metric data operations
type MetricRepositoryInterface interface {
	Create(ctx context.Context, metric *models.Metric) error
	CreateBatch(ctx context.Context, metrics []*models.Metric) error
	GetByID(ctx context.Context, id string) (*models.Metric, error)
	Query(ctx context.Context, query models.MetricQuery) ([]*models.Metric, error)
	Delete(ctx context.Context, id string) error
	DeleteOlderThan(ctx context.Context, cutoffTime string) error
	GetLatestByResource(ctx context.Context, resourceID, metricName string) (*models.Metric, error)
}

// AlertRepositoryInterface defines the contract for alert data operations
type AlertRepositoryInterface interface {
	Create(ctx context.Context, alert *models.Alert) error
	GetByID(ctx context.Context, id string) (*models.Alert, error)
	Update(ctx context.Context, alert *models.Alert) error
	Delete(ctx context.Context, id string) error
	List(ctx context.Context, orgID string, params ListParams) ([]*models.Alert, error)
	Query(ctx context.Context, orgID string, query models.AlertQuery) ([]*models.Alert, error)
	Acknowledge(ctx context.Context, id, userID string) error
	ListUnacknowledged(ctx context.Context, orgID string, params ListParams) ([]*models.Alert, error)
}

// AuditLogRepositoryInterface defines the contract for audit log data operations
type AuditLogRepositoryInterface interface {
	Create(ctx context.Context, log *models.AuditLog) error
	GetByID(ctx context.Context, id string) (*models.AuditLog, error)
	Query(ctx context.Context, orgID string, query models.AuditLogQuery) ([]*models.AuditLog, error)
	Delete(ctx context.Context, id string) error
	DeleteOlderThan(ctx context.Context, cutoffTime string) error
}

// TransactionManager provides transaction management capabilities
type TransactionManager interface {
	WithTransaction(ctx context.Context, fn func(tx *sql.Tx) error) error
}

// ListParams defines common parameters for list operations
type ListParams struct {
	Limit  int
	Offset int
	SortBy string
	Order  string // "asc" or "desc"
	Search string
}

// DefaultListParams returns default list parameters
func DefaultListParams() ListParams {
	return ListParams{
		Limit:  50,
		Offset: 0,
		SortBy: "created_at",
		Order:  "desc",
	}
}

// Validate validates list parameters
func (p *ListParams) Validate() {
	if p.Limit <= 0 || p.Limit > 1000 {
		p.Limit = 50
	}
	if p.Offset < 0 {
		p.Offset = 0
	}
	if p.Order != "asc" && p.Order != "desc" {
		p.Order = "desc"
	}
	if p.SortBy == "" {
		p.SortBy = "created_at"
	}
}